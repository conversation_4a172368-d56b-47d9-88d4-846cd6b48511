import 'package:bitacora/application/sync/machine/steps/download/collection/feed_post/feed_post_by_remote_id_repository_quey.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';

class SyncCollectionFeedPostDownloader extends SyncCollectionDownloader {
  SyncCollectionFeedPostDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.feedPost;

  @override
  bool get supportsBatchOperations => true;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    map['organization_id'] = organization.remoteId!.dbValue;
    final feedPost = apiTranslator.feedPost.fromMap(map);
    return db.feedPost.save(db.context(), feedPost);
  }

  @override
  Future<List<LocalId?>> translateAndSaveBatch(
      List<Map<String, dynamic>> maps) {
    final items =
        maps.map((map) => apiTranslator.feedPost.fromMap(map)).toList();
    return db.feedPost.saveBatch(db.context(), items);
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    await db.transaction((context) async {
      final feedPost = await db
          .query(FeedPostByRemoteIdRepositoryQuery(remoteId), context: context);
      if (feedPost != null) {
        await db.feedPost.delete(context, feedPost.id!);
      }
    });
  }
}
