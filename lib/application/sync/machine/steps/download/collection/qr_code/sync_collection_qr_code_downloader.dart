import 'package:bitacora/application/sync/machine/steps/download/collection/qr_code/qr_code_by_remote_id_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';

class SyncCollectionQrCodeDownloader extends SyncCollectionDownloader {
  SyncCollectionQrCodeDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.qrCode;

  @override
  bool get supportsBatchOperations => true;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    final qrCode = apiTranslator.qrCode.fromMap(map);
    return db.qrCode.save(db.context(), qrCode);
  }

  @override
  Future<List<LocalId?>> translateAndSaveBatch(
      List<Map<String, dynamic>> maps) async {
    final items = maps.map((map) => apiTranslator.qrCode.fromMap(map)).toList();
    return db.qrCode.saveBatch(db.context(), items);
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    final qrCode = await db.query(QrCodeByRemoteIdRepositoryQuery(remoteId));
    if (qrCode != null) {
      await db.qrCode.delete(db.context(), qrCode.id!);
    }
  }
}
