import 'package:bitacora/application/sync/machine/steps/download/collection/signature/signature_by_remote_id_repository_quey.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';

class SyncCollectionSignatureDownloader extends SyncCollectionDownloader {
  SyncCollectionSignatureDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.signature;

  @override
  bool get supportsBatchOperations => true;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    final signature = apiTranslator.signature.fromMap(map);
    return db.signature.save(db.context(), signature);
  }

  @override
  Future<List<LocalId?>> translateAndSaveBatch(List<Map<String, dynamic>> maps) {
    final items = maps
        .map((map) => apiTranslator.signature.fromMap(map))
        .toList();
    return db.signature.saveBatch(db.context(), items);
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    await db.transaction((context) async {
      final signature = await db.query(
          SignatureByRemoteIdRepositoryQuery(remoteId),
          context: context);
      if (signature != null) {
        await db.signature.delete(context, signature.id!);
      }
    });
  }
}
