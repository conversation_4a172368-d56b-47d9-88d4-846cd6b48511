import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/template_condition/template_condition_by_remote_id_repository_query.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';

class SyncCollectionTemplateConditionDownloader
    extends SyncCollectionDownloader {
  SyncCollectionTemplateConditionDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.templateCondition;

  @override
  bool get supportsBatchOperations => true;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    final templateCondition = apiTranslator.templateCondition.fromMap(map);
    return db.templateCondition.save(db.context(), templateCondition);
  }

  @override
  Future<List<LocalId?>> translateAndSaveBatch(
      List<Map<String, dynamic>> maps) {
    final items = maps
        .map((map) => apiTranslator.templateCondition.fromMap(map))
        .toList();
    return db.templateCondition.saveBatch(db.context(), items);
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    await db.transaction((context) async {
      final templateCondition = await db.query(
          TemplateConditionByRemoteIdRepositoryQuery(remoteId),
          context: context);
      if (templateCondition != null) {
        await db.templateCondition.delete(context, templateCondition.id!);
      }
    });
  }
}
