import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/presentation/entry_form/new_fields/new_fields_notifier.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/project_name_suggestion_repository_query.dart';
import 'package:bitacora/presentation/entry_form/util/project_sublocation_form_row/sublocation_suggestion_respository_query.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:bitacora/util/action_on_focus_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

typedef ProjectSublocationTextFieldConfigurationBuilder
    = TypeaheadTextFieldConfiguration Function(
  ValueChanged<String> onSubmitted,
);

class ProjectSublocationForm extends StatefulWidget {
  final String? prefix;
  final Color? prefixColor;
  final ProjectSublocationTextFieldConfigurationBuilder
      projectTextFieldConfigurationBuilder;
  final ProjectSublocationTextFieldConfigurationBuilder
      sublocationTextFieldConfigurationBuilder;
  final TextEditingController? nextController;
  final FocusNode? nextFocusNode;

  const ProjectSublocationForm({
    super.key,
    this.prefix,
    this.prefixColor,
    required this.projectTextFieldConfigurationBuilder,
    required this.sublocationTextFieldConfigurationBuilder,
    this.nextController,
    this.nextFocusNode,
  });

  @override
  State<ProjectSublocationForm> createState() => _ProjectSublocationFormState();
}

class _ProjectSublocationFormState extends State<ProjectSublocationForm> {
  final Key _projectKey = GlobalKey();
  final Key _sublocationKey = GlobalKey();
  QueryScope? _orgDbQueryScope;
  ValueObject<Project?>? _project;
  TypeaheadTextFieldConfiguration? _projectConfiguration;
  TypeaheadTextFieldConfiguration? _sublocationConfiguration;
  bool _isNewFieldProject = false;
  bool _isNewFieldSublocation = false;

  void _initFromBuild(BuildContext context) {
    final activeOrganization = context.watch<ActiveOrganization>();
    if (activeOrganization.value == null) {
      _orgDbQueryScope = null;
      return;
    }

    final db = context.read<Repository>();
    final isFirstInit = _orgDbQueryScope == null;
    _orgDbQueryScope ??= db.queryScope(orgId: activeOrganization.value!.id);

    _initConfiguration(context);
    if (isFirstInit) {
      _validateProjectExists(context, _projectConfiguration!.controller.text);
    }
  }

  void _initConfiguration(BuildContext context) {
    _projectConfiguration = widget.projectTextFieldConfigurationBuilder(
      (value) => _onProjectValueSelected(context, value, true),
    );
    _sublocationConfiguration = widget.sublocationTextFieldConfigurationBuilder(
      (value) => _onSublocationValueSelected(context, value, true),
    );
  }

  @override
  Widget build(BuildContext context) {
    _initFromBuild(context);
    if (_orgDbQueryScope == null) {
      return const SizedBox();
    }

    return _ProjectSublocationFormRowSkeleton(
      prefix: widget.prefix == null
          ? null
          : Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: Text(
                widget.prefix!,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(color: widget.prefixColor),
              ),
            ),
      project: ActionOnFocusWidget(
        focusNode: _projectConfiguration!.focusNode,
        onFocusChanged: (_) => _onProjectFocusChanged(context),
        child: NewableFieldSuggestionTypeahead(
          key: _projectKey,
          suggestionsQuery: const ProjectNameSuggestionRepositoryQuery(),
          queryScopeProvider: (_) async => _orgDbQueryScope!,
          isNewField: _isNewFieldProject,
          textFieldConfiguration: _projectConfiguration!,
          onSelect: (value) => _onProjectValueSelected(context, value, false),
        ),
      ),
      sublocation: ActionOnFocusWidget(
        focusNode: _sublocationConfiguration!.focusNode,
        onFocusChanged: (_) => _onSublocationFocusChanged(context),
        child: NewableFieldSuggestionTypeahead(
          key: _sublocationKey,
          suggestionsQuery: const SublocationSuggestionRepositoryQuery(),
          queryScopeProvider: (db) async => _getSublocationDbQueryScope(db),
          isNewField: _isNewFieldSublocation,
          textFieldConfiguration: _sublocationConfiguration!,
          onSelect: (value) {
            print('Sublocation Value: $value');
            _onSublocationValueSelected(context, value, false);
          },
        ),
      ),
    );
  }

  Future<void> _onProjectValueSelected(
    BuildContext context,
    String value,
    bool wasSubmitted,
  ) async {
    if (_sublocationConfiguration!.controller.text.isEmpty || wasSubmitted) {
      _sublocationConfiguration!.focusNode.requestFocus();
    }
  }

  Future<void> _onSublocationValueSelected(
    BuildContext context,
    String value,
    bool wasSubmitted,
  ) async {
    if (widget.nextController != null && widget.nextController!.text.isEmpty ||
        wasSubmitted) {
      widget.nextFocusNode?.requestFocus();
    }
  }

  void _onProjectFocusChanged(BuildContext context) {
    if (_projectConfiguration!.focusNode.hasFocus) {
      return;
    }

    _validateProjectExists(
        context, _projectConfiguration!.controller.text.trim());
  }

  void _onSublocationFocusChanged(BuildContext context) {
    if (_sublocationConfiguration!.focusNode.hasFocus) {
      return;
    }

    _validateSublocationExists(
      context.read<Repository>(),
      context.read<NewFieldsNotifier>(),
      _sublocationConfiguration!.controller.text.trim(),
    );
  }

  Future<void> _validateProjectExists(
    BuildContext context,
    String value,
  ) async {
    final db = context.read<Repository>();
    final newFieldsNotifier = context.read<NewFieldsNotifier>();
    _project = ValueObject(await _searchProject(db, value));
    await _validateSublocationExists(
      db,
      newFieldsNotifier,
      _sublocationConfiguration!.controller.text,
      _project?.value != null
          ? db.queryScope(projectId: _project!.value!.id)
          : _orgDbQueryScope!,
      _getIsNewFieldProject(),
    );
  }

  Future<void> _validateSublocationExists(
    Repository db,
    NewFieldsNotifier newFieldsNotifier,
    String value, [
    QueryScope? queryScope,
    bool? isNewFieldProject,
  ]) async {
    if (!mounted) {
      return;
    }
    final resolvedScope = queryScope ?? await _getSublocationDbQueryScope(db);
    final result = value.isEmpty
        ? []
        : await db.query(const SublocationSuggestionRepositoryQuery(),
            context:
                db.context(queryScope: resolvedScope.copyWith(pattern: value)));
    _maybeMarkAsNewField(
      newFieldsNotifier,
      result.isEmpty && value.isNotEmpty,
      isNewFieldProject ?? _isNewFieldProject,
    );
  }

  void _maybeMarkAsNewField(NewFieldsNotifier newFieldsNotifier,
      bool isNewFieldSublocation, bool isNewFieldProject) {
    if (!mounted) {
      return;
    }

    if (isNewFieldSublocation == _isNewFieldSublocation &&
        isNewFieldProject == _isNewFieldProject) {
      return;
    }

    setState(() {
      _isNewFieldSublocation = isNewFieldSublocation;
      _isNewFieldProject = isNewFieldProject;
      newFieldsNotifier.markField(
        _projectKey,
        _isNewFieldProject,
      );
      newFieldsNotifier.markField(
        _sublocationKey,
        _isNewFieldSublocation,
      );
    });
  }

  Future<QueryScope> _getSublocationDbQueryScope(Repository db) async {
    final project =
        await _searchProject(db, _projectConfiguration!.controller.text);
    return project != null
        ? db.queryScope(projectId: project.id)
        : _orgDbQueryScope!;
  }

  Future<Project?> _searchProject(Repository db, String value) async {
    return value.isEmpty
        ? null
        : await db.query(ProjectSearchRepositoryQuery(pattern: value),
            context: db.context(queryScope: _orgDbQueryScope));
  }

  bool _getIsNewFieldProject() {
    return _project != null && // _project was searched
        _project!.value == null && // it wasn't found
        _projectConfiguration!.controller.text.trim().isNotEmpty; // not empty
  }
}

class NonEditableProjectSublocationFormRow extends StatelessWidget {
  final String? prefix;
  late final TypeaheadTextFieldConfiguration _projectConfiguration;
  late final TypeaheadTextFieldConfiguration _sublocationConfiguration;

  NonEditableProjectSublocationFormRow({
    super.key,
    this.prefix,
    required ProjectSublocationTextFieldConfigurationBuilder
        projectTextFieldConfigurationBuilder,
    required ProjectSublocationTextFieldConfigurationBuilder
        sublocationTextFieldConfigurationBuilder,
  }) {
    _projectConfiguration = projectTextFieldConfigurationBuilder((_) {});
    _sublocationConfiguration =
        sublocationTextFieldConfigurationBuilder((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return _ProjectSublocationFormRowSkeleton(
      prefix: prefix == null
          ? null
          : Text(
              prefix!,
              style: Theme.of(context).textTheme.bodySmall,
            ),
      project: TextFormField(
        controller: _projectConfiguration.controller,
        decoration: _projectConfiguration.decoration,
      ),
      sublocation: _sublocationConfiguration.controller.text.isEmpty
          ? null
          : TextFormField(
              controller: _sublocationConfiguration.controller,
              decoration: _sublocationConfiguration.decoration,
            ),
    );
  }
}

class _ProjectSublocationFormRowSkeleton extends StatelessWidget {
  final Widget? prefix;
  final Widget project;
  final Widget? sublocation;

  const _ProjectSublocationFormRowSkeleton({
    required this.prefix,
    required this.project,
    required this.sublocation,
  });

  @override
  Widget build(BuildContext context) {
    final nonProjectFlex =
        (prefix == null ? 0 : 2) + (sublocation == null ? 0 : 4);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        if (prefix != null)
          Expanded(
            flex: 2,
            child: prefix!,
          ),
        Expanded(flex: 10 - nonProjectFlex, child: project),
        if (sublocation != null) ...[
          const SizedBox(width: kFormHorizontalSpacing),
          Expanded(flex: 4, child: sublocation!),
        ],
      ],
    );
  }
}
