import 'package:bitacora/presentation/widgets/typeahead_suggestion_highlighted.dart';
import 'package:flutter/material.dart';

typedef TypeaheadOverlayItemBuilder<T> = String Function(T);

class TypeaheadOverlayController<T> {
  final ScrollController scrollController;
  final ValueNotifier<bool> isVerticalDragGesture;
  final FocusNode focusNode;
  final TextEditingController textController;
  final TypeaheadOverlayItemBuilder<T>? itemStringResolver;
  final Color borderColor;
  final Color backgroundColor;
  final Function(T) onSelect;
  final LayerLink layerLink;
  OverlayEntry? _overlayEntry;

  TypeaheadOverlayController({
    required this.scrollController,
    required this.isVerticalDragGesture,
    required this.layerLink,
    required this.focusNode,
    required this.textController,
    required this.onSelect,
    this.itemStringResolver,
    this.borderColor = Colors.transparent,
    this.backgroundColor = Colors.white,
  }) {
    scrollController.addListener(_onScrollListener);
  }

  void dispose() {
    scrollController.removeListener(_onScrollListener);
    hide();
  }

  void _onScrollListener() {
    if (!focusNode.hasFocus) {
      return;
    }

    if (scrollController.position.isScrollingNotifier.value) {
      if (isVerticalDragGesture.value) {
        hide();
        focusNode.unfocus();
      }
    }
  }

  void maybeShow(
    OverlayState overlayState,
    RenderBox parentRenderBox,
    List<T> items,
  ) async {
    if (items.isEmpty) {
      return;
    }

    hide();
    _overlayEntry = _renderOverlay(parentRenderBox, items);
    overlayState.insert(_overlayEntry!);
  }

  void hide() {
    if (_overlayEntry == null) {
      return;
    }

    _overlayEntry!.remove();
    _overlayEntry = null;
  }

  OverlayEntry _renderOverlay(RenderBox parentRenderBox, List<T> items) =>
      OverlayEntry(
        builder: (context) => CompositedTransformFollower(
          link: layerLink,
          showWhenUnlinked: false,
          followerAnchor: Alignment.topCenter,
          targetAnchor: Alignment.bottomCenter,
          child: TypeaheadOverlay(
            parentRenderBox: parentRenderBox,
            borderColor: borderColor,
            backgroundColor: backgroundColor,
            items: items,
            textController: textController,
            focusNode: focusNode,
            itemStringResolver: itemStringResolver,
            onSelect: onSelect,
            onHide: hide,
          ),
        ),
      );
}

class TypeaheadOverlay<T> extends StatefulWidget {
  final RenderBox parentRenderBox;
  final Color borderColor;
  final Color backgroundColor;
  final List<T> items;
  final TextEditingController textController;
  final FocusNode focusNode;
  final TypeaheadOverlayItemBuilder<T>? itemStringResolver;
  final Function(T) onSelect;
  final VoidCallback onHide;

  const TypeaheadOverlay({
    super.key,
    required this.parentRenderBox,
    required this.borderColor,
    required this.backgroundColor,
    required this.items,
    required this.textController,
    this.itemStringResolver,
    required this.onSelect,
    required this.focusNode,
    required this.onHide,
  });

  @override
  State<TypeaheadOverlay<T>> createState() => _TypeaheadOverlayState<T>();
}

class _TypeaheadOverlayState<T> extends State<TypeaheadOverlay<T>> {
  @override
  Widget build(BuildContext context) {
    final borderSide = BorderSide(
      color: widget.borderColor,
      width: 2,
    );
    final borderRadius = BorderRadius.circular(4.0);

    final parentSize = widget.parentRenderBox.size;
    final parentPosition = widget.parentRenderBox.localToGlobal(Offset.zero);
    final screenSize = MediaQuery.sizeOf(context);

    final screenDxCenter = screenSize.width / 2;
    final parentDxCenter = parentPosition.dx + (parentSize.width / 2);

    final horizontalOffset = screenDxCenter - parentDxCenter + 16.0;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(),
        Positioned(
          left: horizontalOffset,
          top: 4.0,
          width: screenSize.width - 32,
          child: Material(
            child: Container(
              constraints: BoxConstraints(maxHeight: 250),
              decoration: BoxDecoration(
                borderRadius: borderRadius,
                color: widget.backgroundColor,
              ),
              child: ClipRRect(
                borderRadius: borderRadius,
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.fromBorderSide(borderSide),
                  ),
                  child: ListView.builder(
                    padding: const EdgeInsets.only(top: 4.0),
                    shrinkWrap: true,
                    itemCount: widget.items.length,
                    itemBuilder: (context, i) {
                      print('itemBuilder $i');
                      return TypeaheadSuggestionHighlighted(
                        pattern: widget.textController.text,
                        suggestion: widget.itemStringResolver == null
                            ? widget.items[i]
                            : (widget.items[i]),
                        highlightColor: Theme.of(context).colorScheme.primary,
                        displayStringResolver: widget.itemStringResolver,
                        onTap: () async {
                          print('onTap');
                          widget.onSelect(widget.items[i]);
                          if (widget.focusNode.hasFocus) {
                            widget.focusNode.unfocus();
                          }
                          widget.onHide();
                        },
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ),
        Positioned(
          left: horizontalOffset + parentPosition.dx - 16.0,
          top: -4.0,
          width: parentSize.width,
          height: 18.0,
          child: AbsorbPointer(
            child: Container(
              decoration: BoxDecoration(
                color: widget.backgroundColor,
              ),
            ),
          ),
        ),
        parentPosition.dx > 16
            ? Positioned(
                left: horizontalOffset + parentPosition.dx - 30.0,
                top: -12.0,
                width: 16.0,
                height: 18.0,
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      right: borderSide,
                      bottom: borderSide,
                    ),
                    borderRadius: BorderRadius.only(
                        bottomRight: borderRadius.bottomRight),
                  ),
                ),
              )
            : Positioned(
                left: horizontalOffset - parentPosition.dx + 16,
                top: -4.0,
                width: 4.0,
                height: 22.0,
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: borderSide,
                    ),
                  ),
                ),
              ),
        parentPosition.dx + parentSize.width >= screenSize.width - 16.0
            ? Positioned(
                left: horizontalOffset +
                    parentPosition.dx +
                    parentSize.width -
                    18.0,
                top: -4.0,
                width: 4.0,
                height: 18.0,
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: borderSide,
                    ),
                  ),
                ),
              )
            : Positioned(
                left: horizontalOffset +
                    parentPosition.dx +
                    parentSize.width -
                    18.0,
                top: -12.0,
                width: 16.0,
                height: 18.0,
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: borderSide,
                      bottom: borderSide,
                    ),
                    borderRadius:
                        BorderRadius.only(bottomLeft: borderRadius.bottomLeft),
                  ),
                ),
              ),
      ],
    );
  }
}
